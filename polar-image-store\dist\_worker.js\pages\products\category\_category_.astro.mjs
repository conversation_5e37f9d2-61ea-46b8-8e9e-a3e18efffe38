globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createAstro, a as createComponent, r as renderComponent, d as renderTemplate, m as maybeRenderHead } from '../../../chunks/astro/server_BfJ9-7B8.mjs';
import { a as $$Layout } from '../../../chunks/Layout_BXXfbbUf.mjs';
import { $ as $$ProductCard } from '../../../chunks/ProductCard_w4CwGM3C.mjs';
import { $ as $$StructuredData } from '../../../chunks/StructuredData_ATPHFzJ4.mjs';
import { c as createPolarClient, t as transformPolarProduct, a as getProductsByCategory, b as getCategoryDisplayName } from '../../../chunks/polar_BfN5RyYf.mjs';
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro("https://infpik.store");
const $$category = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$category;
  const { category } = Astro2.params;
  if (!category) {
    return Astro2.redirect("/products");
  }
  let allProducts = [];
  let products = [];
  let error = null;
  try {
    const env = Astro2.locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ; else {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });
      const productList = response.result?.items || [];
      allProducts = productList.map(transformPolarProduct).filter((product) => product !== null);
      products = getProductsByCategory(allProducts, category);
    }
  } catch (err) {
    console.error("Error fetching products for category:", err);
    error = "Failed to load products";
  }
  const categoryDisplayName = getCategoryDisplayName(category);
  const breadcrumbData = {
    items: [
      { name: "Home", url: "http://infpik.store" },
      { name: "Products", url: `${"http://infpik.store"}/products` },
      { name: categoryDisplayName, url: `${"http://infpik.store"}/products/category/${category}` }
    ]
  };
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${categoryDisplayName} - Polar Image Store`, "description": `Browse our collection of ${categoryDisplayName.toLowerCase()} digital images and artwork. High-quality digital assets for creative projects.`, "canonical": `${"http://infpik.store"}/products/category/${category}` }, { "default": async ($$result2) => renderTemplate`  ${renderComponent($$result2, "StructuredData", $$StructuredData, { "type": "BreadcrumbList", "data": breadcrumbData })} ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <section class="text-center mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4">${categoryDisplayName}</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto"> ${products.length > 0 ? `Discover ${products.length} ${categoryDisplayName.toLowerCase()} ${products.length === 1 ? "item" : "items"} in our collection` : `No ${categoryDisplayName.toLowerCase()} items found`} </p> </section> ${products.length === 0 ? renderTemplate`<div class="text-center py-16"> <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"> <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg> </div> <h3 class="text-2xl font-semibold text-gray-900 mb-4">No ${categoryDisplayName} Found</h3> <p class="text-gray-600 mb-8">We don't have any ${categoryDisplayName.toLowerCase()} items yet.</p> <a href="/products" class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg>
Browse All Products
</a> </div>` : renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${products.map((product) => renderTemplate`${renderComponent($$result2, "ProductCard", $$ProductCard, { "product": product })}`)} </div>`} </div> ` })}`;
}, "D:/code/image/polar-image-store/src/pages/products/category/[category].astro", void 0);
const $$file = "D:/code/image/polar-image-store/src/pages/products/category/[category].astro";
const $$url = "/products/category/[category]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$category,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
