---
import { Image } from 'astro:assets';
import { getOptimizedImageUrl, getResponsiveImageUrls, generateSizesAttribute, type ImageTransformOptions, type ResponsiveImageOptions } from '../utils/imageOptimization';

export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  format?: 'auto' | 'avif' | 'webp' | 'jpeg' | 'png';
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  loading?: 'eager' | 'lazy';
  fetchpriority?: 'high' | 'low' | 'auto';
  class?: string;
  style?: string;
  responsive?: boolean;
  sizes?: number[];
  densities?: number[];
  preset?: 'productCard' | 'productDetail' | 'thumbnail' | 'hero' | 'related';
  [key: string]: any;
}

const {
  src,
  alt,
  width = 800,
  height = 600,
  quality = 85,
  format = 'auto',
  fit = 'scale-down',
  loading = 'lazy',
  fetchpriority = 'auto',
  class: className = '',
  style = '',
  responsive = false,
  sizes = [320, 640, 960, 1280, 1920],
  densities = [1, 2],
  preset,
  ...rest
} = Astro.props;

// Use preset if provided
let optimizedSrc: string;
let srcset: string | undefined;

if (preset) {
  // Import presets dynamically
  const { ImagePresets } = await import('../utils/imageOptimization');
  optimizedSrc = ImagePresets[preset](src);
} else if (responsive) {
  // Generate responsive images
  const responsiveOptions: ResponsiveImageOptions = {
    sizes,
    densities,
    width,
    height,
    quality,
    format,
    fit
  };
  
  const responsiveUrls = getResponsiveImageUrls(src, responsiveOptions);
  optimizedSrc = responsiveUrls.src;
  srcset = responsiveUrls.srcset;
} else {
  // Single optimized image
  const transformOptions: ImageTransformOptions = {
    width,
    height,
    quality,
    format,
    fit
  };
  
  optimizedSrc = getOptimizedImageUrl(src, transformOptions);
}

// Generate sizes attribute for responsive images
const sizesAttr = responsive ? generateSizesAttribute() : undefined;

// Check if it's a local image (starts with /) or external
const isLocalImage = src.startsWith('/') || src.includes('placeholder');
---

{isLocalImage ? (
  <!-- Use Astro's built-in Image component for local images -->
  <Image
    src={src}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    fetchpriority={fetchpriority}
    class={className}
    style={style}
    {...rest}
  />
) : (
  <!-- Use optimized external image with Cloudflare Image Transform -->
  <img
    src={optimizedSrc}
    srcset={srcset}
    sizes={sizesAttr}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    fetchpriority={fetchpriority}
    class={className}
    style={style}
    {...rest}
  />
)}
