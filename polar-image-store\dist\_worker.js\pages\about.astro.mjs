globalThis.process ??= {}; globalThis.process.env ??= {};
import { a as createComponent, r as renderComponent, d as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_BfJ9-7B8.mjs';
import { a as $$Layout } from '../chunks/Layout_BXXfbbUf.mjs';
export { renderers } from '../renderers.mjs';

const $$About = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "About Us - Polar Image Store" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container py-12"> <div class="max-w-4xl mx-auto"> <h1 class="text-4xl font-bold text-primary-900 mb-8">About Us</h1> <div class="prose prose-lg max-w-none"> <p class="lead text-xl text-primary-700 mb-6">
Welcome to Polar Image Store, your premier destination for high-quality digital images and artwork.
</p> <h2>Our Story</h2> <p>
Founded in 2025, Polar Image Store was born from a passion for visual storytelling and a desire to connect talented artists with those who appreciate their work. What began as a small collection has grown into a curated gallery of premium digital assets.
</p> <h2>Our Mission</h2> <p>
At Polar Image Store, our mission is to provide a platform where creativity thrives. We believe in the power of visual content to inspire, communicate, and transform. By offering a diverse range of high-quality images, we aim to help creators, businesses, and individuals find the perfect visual assets for their projects.
</p> <h2>Our Collection</h2> <p>
Our carefully curated collection spans various categories including photography, digital art, illustrations, abstract designs, nature scenes, portraits, and landscapes. Each piece in our collection is selected for its quality, originality, and visual impact.
</p> <h2>Our Team</h2> <p>
Behind Polar Image Store is a team of passionate individuals dedicated to promoting visual arts. Our team includes curators, designers, and technology experts working together to create an exceptional experience for both artists and customers.
</p> <h2>Quality Commitment</h2> <p>
We are committed to maintaining the highest standards of quality in our collection. All images undergo a rigorous selection process to ensure they meet our standards for technical excellence and artistic merit.
</p> <h2>Connect With Us</h2> <p>
We love hearing from our community! Whether you have questions, feedback, or just want to say hello, feel free to reach out to us through our contact channels.
</p> <div class="mt-8 p-6 bg-primary-50 rounded-xl"> <h3 class="text-xl font-semibold text-primary-900 mb-4">Contact Information</h3> <p><strong>Email:</strong> <a href="mailto:<EMAIL>" class="text-accent-600 hover:text-accent-700"><EMAIL></a></p> <p><strong>Hours:</strong> Monday to Friday, 9:00 AM - 5:00 PM (EST)</p> </div> </div> </div> </div> ` })}`;
}, "D:/code/image/polar-image-store/src/pages/about.astro", void 0);

const $$file = "D:/code/image/polar-image-store/src/pages/about.astro";
const $$url = "/about";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$About,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
