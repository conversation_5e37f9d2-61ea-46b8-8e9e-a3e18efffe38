// @ts-check
import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import cloudflare from '@astrojs/cloudflare';
import sitemap from '@astrojs/sitemap';

// https://astro.build/config
export default defineConfig({
  site: 'https://infpik.store', // Required for sitemap
  output: 'server', // Changed from 'static' to 'server' to support API routes on Cloudflare Pages
  adapter: cloudflare({
    platformProxy: {
      enabled: true,
      configPath: 'wrangler.jsonc',
      persist: {
        path: './.cache/wrangler/v3'
      },
    },
    imageService: 'compile',
  }),
  integrations: [
    sitemap({
      // Customize sitemap generation
      filter: (page) => !page.includes('/api/'),
      customPages: [],
      i18n: {
        defaultLocale: 'en',
        locales: {
          en: 'en-US',
        },
      },
    }),
  ],
  vite: {
    plugins: [tailwindcss()],
  },
});
