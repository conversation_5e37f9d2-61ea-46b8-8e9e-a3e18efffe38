globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createPolarClient, t as transformPolarProduct, e as extractUniqueTags, g as getTagDisplayName } from '../../chunks/polar_CPL-REiF.mjs';
export { renderers } from '../../renderers.mjs';

const prerender = false;
const GET = async ({ url, locals }) => {
  try {
    const query = url.searchParams.get("q");
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ;
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });
    const productList = response.result?.items || [];
    const products = productList.map(transformPolarProduct).filter((product) => product !== null);
    const allTags = extractUniqueTags(products);
    if (!query || query.trim().length < 2) {
      const popularTags = allTags.map((tag) => {
        const tagProducts = products.filter(
          (product) => product.tags && product.tags.includes(tag)
        );
        return {
          id: tag,
          name: getTagDisplayName(tag),
          displayName: getTagDisplayName(tag),
          count: tagProducts.length,
          url: `/products/tag/${encodeURIComponent(tag)}`
        };
      }).sort((a, b) => b.count - a.count).slice(0, 10);
      return new Response(
        JSON.stringify({
          results: [],
          popularTags
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "public, max-age=300"
            // Cache for 5 minutes
          }
        }
      );
    }
    const searchTerm = query.toLowerCase().trim();
    const matchingTags = allTags.filter((tag) => {
      const tagName = getTagDisplayName(tag).toLowerCase();
      const tagId = tag.toLowerCase();
      return tagId.includes(searchTerm) || tagName.includes(searchTerm);
    });
    const tagResults = matchingTags.map((tag) => {
      const tagProducts = products.filter(
        (product) => product.tags && product.tags.includes(tag)
      );
      return {
        id: tag,
        name: getTagDisplayName(tag),
        displayName: getTagDisplayName(tag),
        count: tagProducts.length,
        url: `/products/tag/${encodeURIComponent(tag)}`
      };
    });
    tagResults.sort((a, b) => {
      const aExactMatch = a.name.toLowerCase() === searchTerm || a.id.toLowerCase() === searchTerm;
      const bExactMatch = b.name.toLowerCase() === searchTerm || b.id.toLowerCase() === searchTerm;
      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;
      if (b.count !== a.count) {
        return b.count - a.count;
      }
      return a.name.localeCompare(b.name);
    });
    return new Response(
      JSON.stringify({
        results: tagResults,
        total: tagResults.length,
        query
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=60"
          // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    console.error("Search API error:", error);
    return new Response(
      JSON.stringify({ error: "Search failed" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
