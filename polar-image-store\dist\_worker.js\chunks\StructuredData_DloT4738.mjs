globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createAstro, a as createComponent, r as renderComponent, d as renderTemplate, m as maybeRenderHead, b as addAttribute, s as spreadAttributes, u as unescapeHTML } from './astro/server_BdgiS2eL.mjs';
import { $ as $$Image } from './_astro_assets_BhKiY6XE.mjs';
import { getResponsiveImageUrls, getOptimizedImageUrl, generateSizesAttribute } from './imageOptimization_DKmSV198.mjs';

const $$Astro$1 = createAstro("https://infpik.store");
const $$OptimizedImage = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$OptimizedImage;
  const {
    src,
    alt,
    width = 800,
    height = 600,
    quality = 85,
    format = "auto",
    fit = "scale-down",
    loading = "lazy",
    fetchpriority = "auto",
    class: className = "",
    style = "",
    responsive = false,
    sizes = [320, 640, 960, 1280, 1920],
    densities = [1, 2],
    preset,
    ...rest
  } = Astro2.props;
  let optimizedSrc;
  let srcset;
  if (preset) {
    const { ImagePresets } = await import('./imageOptimization_DKmSV198.mjs');
    optimizedSrc = ImagePresets[preset](src);
  } else if (responsive) {
    const responsiveOptions = {
      sizes,
      densities,
      width,
      height,
      quality,
      format,
      fit
    };
    const responsiveUrls = getResponsiveImageUrls(src, responsiveOptions);
    optimizedSrc = responsiveUrls.src;
    srcset = responsiveUrls.srcset;
  } else {
    const transformOptions = {
      width,
      height,
      quality,
      format,
      fit
    };
    optimizedSrc = getOptimizedImageUrl(src, transformOptions);
  }
  const sizesAttr = responsive ? generateSizesAttribute() : void 0;
  const isLocalImage = src.startsWith("/") || src.includes("placeholder");
  return renderTemplate`${isLocalImage ? renderTemplate`<!-- Use Astro's built-in Image component for local images -->
  ${renderComponent($$result, "Image", $$Image, { "src": src, "alt": alt, "width": width, "height": height, "loading": loading, "fetchpriority": fetchpriority, "class": className, "style": style, ...rest })}` : renderTemplate`<!-- Use optimized external image with Cloudflare Image Transform -->
  ${maybeRenderHead()}<img${addAttribute(optimizedSrc, "src")}${addAttribute(srcset, "srcset")}${addAttribute(sizesAttr, "sizes")}${addAttribute(alt, "alt")}${addAttribute(width, "width")}${addAttribute(height, "height")}${addAttribute(loading, "loading")}${addAttribute(fetchpriority, "fetchpriority")}${addAttribute(className, "class")}${addAttribute(style, "style")}${spreadAttributes(rest)}>`}`;
}, "D:/code/image/polar-image-store/src/components/OptimizedImage.astro", void 0);

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$Astro = createAstro("https://infpik.store");
const $$StructuredData = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$StructuredData;
  const { type, data } = Astro2.props;
  function generateStructuredData(type2, data2) {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type2
    };
    switch (type2) {
      case "Product":
        return {
          ...baseData,
          name: data2.name,
          description: data2.description,
          image: data2.images || [],
          sku: data2.id,
          brand: {
            "@type": "Brand",
            name: "Polar Image Store"
          },
          offers: {
            "@type": "Offer",
            price: data2.price,
            priceCurrency: data2.currency || "USD",
            availability: data2.isAvailable ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
            seller: {
              "@type": "Organization",
              name: "Polar Image Store",
              url: "https://infpik.store"
            },
            url: data2.url
          },
          category: "Digital Images",
          productID: data2.id,
          ...data2.aggregateRating && {
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: data2.aggregateRating.ratingValue,
              reviewCount: data2.aggregateRating.reviewCount
            }
          }
        };
      case "Organization":
        return {
          ...baseData,
          name: "Polar Image Store",
          url: "https://infpik.store",
          logo: "https://infpik.store/favicon.svg",
          description: "Premium digital images and artwork for creative projects",
          sameAs: [
            "https://twitter.com/polarimagestore",
            "https://facebook.com/polarimagestore"
          ],
          contactPoint: {
            "@type": "ContactPoint",
            contactType: "customer service",
            email: "<EMAIL>"
          }
        };
      case "WebSite":
        return {
          ...baseData,
          name: "Polar Image Store",
          url: "https://infpik.store",
          description: "Premium digital images and artwork for creative projects",
          publisher: {
            "@type": "Organization",
            name: "Polar Image Store"
          },
          potentialAction: {
            "@type": "SearchAction",
            target: "https://infpik.store/products?search={search_term_string}",
            "query-input": "required name=search_term_string"
          }
        };
      case "BreadcrumbList":
        return {
          ...baseData,
          itemListElement: data2.items.map((item, index) => ({
            "@type": "ListItem",
            position: index + 1,
            name: item.name,
            item: item.url
          }))
        };
      default:
        return baseData;
    }
  }
  const structuredData = generateStructuredData(type, data);
  return renderTemplate(_a || (_a = __template(['<script type="application/ld+json">', "<\/script>"])), unescapeHTML(JSON.stringify(structuredData)));
}, "D:/code/image/polar-image-store/src/components/StructuredData.astro", void 0);

export { $$OptimizedImage as $, $$StructuredData as a };
