globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createAstro, a as createComponent, m as maybeRenderHead, r as renderComponent, f as renderScript, b as addAttribute, d as renderTemplate } from '../../chunks/astro/server_BfJ9-7B8.mjs';
import { $ as $$Image, a as $$Layout } from '../../chunks/Layout_BXXfbbUf.mjs';
/* empty css                                     */
import { $ as $$StructuredData } from '../../chunks/StructuredData_ATPHFzJ4.mjs';
import { c as createPolarClient, t as transformPolarProduct, i as formatPrice } from '../../chunks/polar_BfN5RyYf.mjs';
export { renderers } from '../../renderers.mjs';

const $$Astro$2 = createAstro("https://infpik.store");
const $$ImageGallery = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$ImageGallery;
  const { images, productName } = Astro2.props;
  const displayImages = images.length > 0 ? images : ["/placeholder-image.svg"];
  return renderTemplate`${maybeRenderHead()}<div class="flex flex-col gap-4" data-astro-cid-gjhjmbi3> <div class="relative aspect-video rounded-2xl overflow-hidden bg-gray-50 cursor-zoom-in flex items-center justify-center group" data-astro-cid-gjhjmbi3> ${renderComponent($$result, "Image", $$Image, { "id": "mainImage", "src": displayImages[0], "alt": productName, "width": 800, "height": 600, "loading": "eager", "fetchpriority": "high", "onclick": "openLightbox(0)", "class": "max-w-full max-h-full w-auto h-auto object-contain transition-transform duration-300 group-hover:scale-105", "data-astro-cid-gjhjmbi3": true })} </div> ${displayImages.length > 1 && renderTemplate`<div class="grid grid-cols-[repeat(auto-fit,minmax(80px,1fr))] gap-2 max-h-24 overflow-x-auto" data-astro-cid-gjhjmbi3> ${displayImages.map((image, index) => renderTemplate`<div${addAttribute(`aspect-square rounded-full overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:border-primary-500 hover:scale-105 ${index === 0 ? "border-primary-500" : "border-transparent"}`, "class")} data-astro-cid-gjhjmbi3> ${renderComponent($$result, "Image", $$Image, { "src": image, "alt": `${productName} - Image ${index + 1}`, "width": 80, "height": 80, "loading": "eager", "onclick": `changeMainImage('${image}', ${index})`, "class": "w-full h-full object-cover", "data-astro-cid-gjhjmbi3": true })} </div>`)} </div>`} </div> <!-- Lightbox Modal --> <div id="lightbox" class="hidden fixed inset-0 z-[1000] bg-black/90 animate-fade-in" data-astro-cid-gjhjmbi3> <div class="relative max-w-[90%] max-h-[90%] flex items-center justify-center h-full mx-auto" data-astro-cid-gjhjmbi3> <button class="absolute -top-12 right-0 text-white text-3xl font-bold bg-none border-none cursor-pointer z-[1001] hover:text-gray-300 transition-colors" onclick="closeLightbox()" data-astro-cid-gjhjmbi3>
&times;
</button> <button class="absolute top-1/2 -translate-y-1/2 -left-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="prevImage()" data-astro-cid-gjhjmbi3>
&#8249;
</button> ${renderComponent($$result, "Image", $$Image, { "id": "lightboxImage", "src": "/placeholder-image.svg", "alt": "Lightbox image", "width": 1200, "height": 800, "loading": "lazy", "class": "max-w-full max-h-full object-contain rounded-lg", "data-astro-cid-gjhjmbi3": true })} <button class="absolute top-1/2 -translate-y-1/2 -right-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="nextImage()" data-astro-cid-gjhjmbi3>
&#8250;
</button> <div class="absolute -bottom-12 left-1/2 -translate-x-1/2 text-white bg-black/70 px-4 py-2 rounded-full" data-astro-cid-gjhjmbi3> <span id="imageCounter" data-astro-cid-gjhjmbi3>1 / 1</span> </div> </div> </div>  ${renderScript($$result, "D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/code/image/polar-image-store/src/components/ImageGallery.astro", void 0);

const $$Astro$1 = createAstro("https://infpik.store");
const $$RelatedImages = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$RelatedImages;
  const { currentProduct } = Astro2.props;
  function calculateSimilarity(product1, product2) {
    if (!product1.tags || !product2.tags) return 0;
    const tags1 = new Set(product1.tags);
    const tags2 = new Set(product2.tags);
    let sharedTags = 0;
    for (const tag of tags1) {
      if (tags2.has(tag)) {
        sharedTags++;
      }
    }
    return sharedTags;
  }
  let relatedProducts = [];
  try {
    const polar = createPolarClient();
    const organizationId = "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (organizationId && currentProduct.tags && currentProduct.tags.length > 0) {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });
      const productList = response.result?.items || [];
      const allProducts = productList.map(transformPolarProduct).filter((product) => product !== null);
      const candidateProducts = allProducts.filter(
        (product) => product.id !== currentProduct.id && product.tags && product.tags.length > 0
      );
      const productsWithScores = candidateProducts.map((product) => ({
        product,
        similarity: calculateSimilarity(currentProduct, product)
      })).filter((item) => item.similarity > 0).sort((a, b) => b.similarity - a.similarity).slice(0, 6).map((item) => item.product);
      relatedProducts = productsWithScores;
    }
  } catch (error) {
    console.error("Error fetching related products:", error);
  }
  return renderTemplate`${relatedProducts.length > 0 && renderTemplate`${maybeRenderHead()}<section class="mt-16" data-astro-cid-4j3m3ndg><div class="mb-8" data-astro-cid-4j3m3ndg><h2 class="text-3xl font-bold text-primary-900 mb-4" data-astro-cid-4j3m3ndg>Related Images</h2><p class="text-primary-600" data-astro-cid-4j3m3ndg>Discover similar images you might like</p></div><div class="relative" data-astro-cid-4j3m3ndg><!-- Slider container --><div class="overflow-x-auto scrollbar-hide" data-astro-cid-4j3m3ndg><div class="flex gap-6 pb-4" style="width: max-content;" data-astro-cid-4j3m3ndg>${relatedProducts.map((product) => renderTemplate`<div class="flex-none w-80" data-astro-cid-4j3m3ndg><div class="group bg-white rounded-2xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-xl hover:shadow-primary-500/10" data-astro-cid-4j3m3ndg>${product.images.length > 0 && renderTemplate`<div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50" data-astro-cid-4j3m3ndg>${renderComponent($$result, "Image", $$Image, { "src": product.images[0], "alt": product.name, "width": 600, "height": 450, "loading": "lazy", "class": "w-full h-full object-cover transition-all duration-300 group-hover:scale-105", "data-astro-cid-4j3m3ndg": true })}<!-- Gradient overlay --><div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" data-astro-cid-4j3m3ndg></div><!-- Hover actions --><div class="absolute inset-0 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0" data-astro-cid-4j3m3ndg><a${addAttribute(`/products/${product.slug}`, "href")} class="flex items-center gap-2 px-4 py-2 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-medium text-sm transition-all duration-200 hover:bg-white hover:scale-105" data-astro-cid-4j3m3ndg><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-4j3m3ndg><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" data-astro-cid-4j3m3ndg></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" data-astro-cid-4j3m3ndg></path></svg>
View
</a></div><!-- Price badge --><div class="absolute top-3 right-3 px-2 py-1 bg-white/95 backdrop-blur-sm rounded-full" data-astro-cid-4j3m3ndg><span class="text-sm font-bold text-primary-900" data-astro-cid-4j3m3ndg>${formatPrice(product.price, product.currency)}</span></div></div>`}<div class="p-4" data-astro-cid-4j3m3ndg><!-- Tags -->${product.tags && product.tags.length > 0 && renderTemplate`<div class="mb-2" data-astro-cid-4j3m3ndg><div class="flex flex-wrap gap-1" data-astro-cid-4j3m3ndg>${product.tags.slice(0, 2).map((tag) => renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-accent-100 text-accent-700 text-xs font-medium rounded-full" data-astro-cid-4j3m3ndg>
#${tag}</span>`)}${product.tags.length > 2 && renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-primary-100 text-primary-600 text-xs font-medium rounded-full" data-astro-cid-4j3m3ndg>
+${product.tags.length - 2}</span>`}</div></div>`}<!-- Title --><h3 class="text-lg font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors" data-astro-cid-4j3m3ndg>${product.name}</h3><!-- Description --><p class="text-primary-600 text-sm line-clamp-2 leading-relaxed" data-astro-cid-4j3m3ndg>${product.description}</p></div></div></div>`)}</div></div><!-- Scroll indicators (optional) --><div class="flex justify-center mt-4 gap-2" data-astro-cid-4j3m3ndg>${relatedProducts.map((_, index) => renderTemplate`<div class="w-2 h-2 bg-primary-200 rounded-full" data-astro-cid-4j3m3ndg></div>`)}</div></div></section>`}`;
}, "D:/code/image/polar-image-store/src/components/RelatedImages.astro", void 0);

const $$Astro = createAstro("https://infpik.store");
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/products");
  }
  let product = null;
  let error = null;
  try {
    const env = Astro2.locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ; else {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });
      const productList = response.result?.items || [];
      const products = productList.map(transformPolarProduct).filter((product2) => product2 !== null);
      product = products.find((p) => p.slug === slug) || null;
      if (!product) {
        return Astro2.redirect("/products");
      }
    }
  } catch (err) {
    console.error("Error fetching product:", err);
    return Astro2.redirect("/products");
  }
  if (!product || !product.id || !product.name) {
    return Astro2.redirect("/products");
  }
  let checkoutUrl = "";
  try {
    const env = Astro2.locals?.runtime?.env;
    const polar = createPolarClient(env);
    const checkoutLink = await polar.checkoutLinks.create({
      paymentProcessor: "stripe",
      productId: product.id,
      allowDiscountCodes: true,
      requireBillingAddress: false,
      successUrl: `${"http://infpik.store"}/success`
    });
    checkoutUrl = checkoutLink.url;
  } catch (error2) {
    console.error("Error creating checkout URL:", error2);
  }
  const productUrl = `${"http://infpik.store"}/products/${product.slug}`;
  const productStructuredData = {
    name: product.name,
    description: product.description,
    images: product.images,
    price: product.price,
    currency: product.currency,
    isAvailable: product.isAvailable,
    id: product.id,
    url: productUrl
  };
  const breadcrumbData = {
    items: [
      { name: "Home", url: "http://infpik.store" },
      { name: "Products", url: `${"http://infpik.store"}/products` },
      { name: product.name, url: productUrl }
    ]
  };
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${product.name} - Polar Image Store`, "description": product.description, "image": product.images[0] || "/og-image.jpg", "canonical": productUrl, "type": "product" }, { "default": async ($$result2) => renderTemplate`  ${renderComponent($$result2, "StructuredData", $$StructuredData, { "type": "Product", "data": productStructuredData })} ${renderComponent($$result2, "StructuredData", $$StructuredData, { "type": "BreadcrumbList", "data": breadcrumbData })} ${maybeRenderHead()}<div class="container max-w-7xl"> <div class="mt-8 mb-8"> <nav class="flex items-center gap-2 text-sm text-primary-600"> <a href="/" class="hover:text-accent-600 transition-colors">Home</a> <span>/</span> <a href="/products" class="hover:text-accent-600 transition-colors">Products</a> <span>/</span> <span class="text-primary-900 font-medium">${product.name}</span> </nav> </div> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12"> <div class="lg:sticky lg:top-8 lg:self-start"> ${renderComponent($$result2, "ImageGallery", $$ImageGallery, { "images": product.images, "productName": product.name })} </div> <div class="space-y-8"> <div> <h1 class="text-3xl lg:text-4xl font-bold text-primary-900 mb-6">${product.name}</h1> </div> <div class="prose prose-gray max-w-none"> <h3 class="text-lg font-semibold text-primary-900 mb-3">Description</h3> <p class="text-primary-700 leading-relaxed">${product.description}</p> </div> ${product.tags && product.tags.length > 0 && renderTemplate`<div> <h3 class="text-lg font-semibold text-gray-900 mb-3">Similar</h3> <div class="flex flex-wrap gap-2"> ${product.tags.map((tag) => renderTemplate`<a${addAttribute(`/products/tag/${tag}`, "href")} class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 hover:text-gray-900 transition-colors duration-200 cursor-pointer">
#${tag} </a>`)} </div> </div>`} <div class="pt-6 border-t border-primary-200"> <div class="flex gap-3"> ${checkoutUrl ? renderTemplate`<a${addAttribute(checkoutUrl, "href")} class="flex-1 inline-flex items-center justify-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path> </svg> <span>Buy Now - ${formatPrice(product.price, product.currency)}</span> </a>` : renderTemplate`<button class="flex-1 bg-primary-300 text-primary-600 px-6 py-3 rounded-full font-semibold text-base cursor-not-allowed" disabled>
Checkout Unavailable
</button>`} <button class="inline-flex items-center justify-center gap-2 bg-primary-50 border-2 border-primary-200 text-primary-700 px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-primary-100 hover:border-primary-300 hover:text-primary-900" onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href)"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path> </svg> <span>Share</span> </button> </div> </div> <div class="bg-primary-50 rounded-2xl p-6"> <h3 class="text-lg font-semibold text-primary-900 mb-4">Product Details</h3> <ul class="space-y-3 text-primary-700"> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Format:</strong> High-resolution digital image</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>License:</strong> Commercial use allowed</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Delivery:</strong> Instant download after purchase</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Support:</strong> Email support included</span> </li> </ul> </div> </div> </div> <!-- Related Images Section --> ${renderComponent($$result2, "RelatedImages", $$RelatedImages, { "currentProduct": product })} <div class="mt-16 text-center"> <h2 class="text-3xl font-bold text-primary-900 mb-4">You might also like</h2> <p class="text-primary-600 mb-8">Browse our full collection of digital images</p> <a href="/products" class="inline-flex items-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5">
View All Products
<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path> </svg> </a> </div> </div> ` })}`;
}, "D:/code/image/polar-image-store/src/pages/products/[slug].astro", void 0);
const $$file = "D:/code/image/polar-image-store/src/pages/products/[slug].astro";
const $$url = "/products/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
