---
export interface Tag {
  id: string;
  name: string;
  count?: number;
}

export interface Props {
  tags: Tag[];
  activeTag?: string;
}

const {
  tags,
  activeTag = 'all'
} = Astro.props;
---

<section class="py-6 bg-primary-50 border-b border-primary-200">
  <div class="container">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-primary-900 mb-2">Browse by Tags</h3>
      <p class="text-sm text-primary-600">Find specific content with detailed tags</p>
    </div>

    <!-- Tag Navigation -->
    <div class="relative">
      <!-- Left scroll button -->
      <button
        class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 w-8 h-8 bg-white shadow-lg rounded-full flex items-center justify-center text-primary-600 hover:text-primary-900 transition-all opacity-0 pointer-events-none z-10"
        id="scrollLeft"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <!-- Scroll container -->
      <div class="overflow-x-auto scrollbar-hide" id="tagScroll">
        <div class="flex gap-2 pb-2 min-w-max">
          {tags.map((tag) => (
            <button
              class={`tag-tab flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all whitespace-nowrap ${
                activeTag === tag.id
                  ? 'bg-accent-600 text-white shadow-md'
                  : 'bg-primary-50 text-primary-900 border border-primary-200 hover:bg-primary-100 hover:text-primary-900'
              }`}
              data-tag={tag.id}
            >
              {tag.name}
              {tag.count && (
                <span class={`text-xs px-1.5 py-0.5 rounded-full ${
                  activeTag === tag.id
                    ? 'bg-white/20 text-white'
                    : 'bg-primary-200 text-primary-600'
                }`}>
                  {tag.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      <!-- Right scroll button -->
      <button
        class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-2 w-8 h-8 bg-white shadow-lg rounded-full flex items-center justify-center text-primary-600 hover:text-primary-900 transition-all opacity-0 pointer-events-none z-10"
        id="scrollRight"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  </div>
</section>

<style>
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollContainer = document.getElementById('tagScroll');
    const scrollLeftBtn = document.getElementById('scrollLeft');
    const scrollRightBtn = document.getElementById('scrollRight');
    const tagTabs = document.querySelectorAll('.tag-tab');

    if (!scrollContainer || !scrollLeftBtn || !scrollRightBtn) return;

    // Check if scrolling is needed and update arrow buttons
    function updateScrollButtons() {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;

      if (scrollWidth > clientWidth) {
        scrollLeftBtn.style.opacity = scrollLeft > 0 ? '1' : '0';
        scrollLeftBtn.style.pointerEvents = scrollLeft > 0 ? 'auto' : 'none';

        scrollRightBtn.style.opacity = scrollLeft < scrollWidth - clientWidth ? '1' : '0';
        scrollRightBtn.style.pointerEvents = scrollLeft < scrollWidth - clientWidth ? 'auto' : 'none';
      } else {
        scrollLeftBtn.style.opacity = '0';
        scrollLeftBtn.style.pointerEvents = 'none';
        scrollRightBtn.style.opacity = '0';
        scrollRightBtn.style.pointerEvents = 'none';
      }
    }

    // Scroll functions
    scrollLeftBtn.addEventListener('click', () => {
      scrollContainer.scrollBy({ left: -200, behavior: 'smooth' });
    });

    scrollRightBtn.addEventListener('click', () => {
      scrollContainer.scrollBy({ left: 200, behavior: 'smooth' });
    });

    // Update buttons on scroll and resize
    scrollContainer.addEventListener('scroll', updateScrollButtons);
    window.addEventListener('resize', updateScrollButtons);

    // Initial check
    updateScrollButtons();

    // Enable touch scrolling for mobile devices
    let isDown = false;
    let startX;
    let scrollLeft;

    scrollContainer.addEventListener('touchstart', (e) => {
      isDown = true;
      startX = e.touches[0].pageX - scrollContainer.offsetLeft;
      scrollLeft = scrollContainer.scrollLeft;
    });

    scrollContainer.addEventListener('touchend', () => {
      isDown = false;
    });

      scrollContainer.addEventListener('touchmove', (e) => {
        if (!isDown) return;
        const x = e.touches[0].pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Also enable mouse drag scrolling for desktop
      scrollContainer.addEventListener('mousedown', (e) => {
        isDown = true;
        startX = e.pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
        scrollContainer.style.cursor = 'grabbing';
      });

      scrollContainer.addEventListener('mouseleave', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mouseup', () => {
        isDown = false;
        scrollContainer.style.cursor = 'grab';
      });

      scrollContainer.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2; // Scroll speed multiplier
        scrollContainer.scrollLeft = scrollLeft - walk;
      });
      
      // Set initial cursor
      scrollContainer.style.cursor = 'grab';
    }

    // Tag tab click handlers
      tagTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          const tagId = e.currentTarget.dataset.tag;

          // Remove active class from all tabs
          tagTabs.forEach(t => {
            t.classList.remove('bg-accent-600', 'text-white', 'shadow-md');
            t.classList.add('bg-primary-50', 'text-primary-900', 'border', 'border-primary-200');

            // Update count badge
            const badge = t.querySelector('span');
            if (badge) {
              badge.classList.remove('bg-white/20', 'text-white');
              badge.classList.add('bg-primary-200', 'text-primary-600');
            }
          });

          // Add active class to clicked tab
          e.currentTarget.classList.remove('bg-primary-50', 'text-primary-900', 'border', 'border-primary-200');
          e.currentTarget.classList.add('bg-accent-600', 'text-white', 'shadow-md');

          // Update count badge for active tab
          const activeBadge = e.currentTarget.querySelector('span');
          if (activeBadge) {
            activeBadge.classList.remove('bg-primary-200', 'text-primary-600');
            activeBadge.classList.add('bg-white/20', 'text-white');
          }

          // Dispatch custom event for filtering
          window.dispatchEvent(new CustomEvent('tagChange', {
            detail: { tagId }
          }));
        });
      });
    }
  });
</script>
