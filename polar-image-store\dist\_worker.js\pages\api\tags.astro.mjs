globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createPolarClient, t as transformPolarProduct, e as extractUniqueTags, g as getTagDisplayName } from '../../chunks/polar_BfN5RyYf.mjs';
export { renderers } from '../../renderers.mjs';

const prerender = false;
const GET = async ({ url, locals }) => {
  try {
    const query = url.searchParams.get("q") || "";
    if (!query || query.trim().length < 1) {
      return new Response(
        JSON.stringify({ results: [] }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "public, max-age=60"
          }
        }
      );
    }
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ;
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });
    const productList = response.result?.items || [];
    const products = productList.map(transformPolarProduct).filter((product) => product !== null);
    const allTags = extractUniqueTags(products);
    const searchTerm = query.toLowerCase().trim();
    const matchingTags = allTags.filter((tag) => {
      const tagName = getTagDisplayName(tag).toLowerCase();
      const tagId = tag.toLowerCase();
      return tagId.includes(searchTerm) || tagName.includes(searchTerm);
    });
    const tagResults = matchingTags.map((tag) => {
      const productCount = products.filter(
        (product) => product.tags && product.tags.includes(tag)
      ).length;
      return {
        id: tag,
        name: getTagDisplayName(tag),
        displayName: `#${getTagDisplayName(tag)}`,
        count: productCount,
        url: `/products/tag/${tag}`
      };
    });
    tagResults.sort((a, b) => {
      const aExactMatch = a.name.toLowerCase() === searchTerm || a.id.toLowerCase() === searchTerm;
      const bExactMatch = b.name.toLowerCase() === searchTerm || b.id.toLowerCase() === searchTerm;
      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;
      if (b.count !== a.count) {
        return b.count - a.count;
      }
      return a.name.localeCompare(b.name);
    });
    const results = tagResults.slice(0, 8);
    return new Response(
      JSON.stringify({
        results,
        total: matchingTags.length,
        query
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=60"
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: "Tag search failed",
        details: error instanceof Error ? error.message : "Unknown error"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
