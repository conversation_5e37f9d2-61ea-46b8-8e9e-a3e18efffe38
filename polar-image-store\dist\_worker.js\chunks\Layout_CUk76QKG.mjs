globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createAstro, a as createComponent, b as addAttribute, d as renderTemplate, s as spreadAttributes, u as unescapeHTML, r as renderComponent, m as maybeRenderHead, f as renderScript, h as renderHead, e as renderSlot } from './astro/server_BdgiS2eL.mjs';
/* empty css                         */
import { $ as $$Image } from './_astro_assets_BhKiY6XE.mjs';

const $$Astro$8 = createAstro("https://infpik.store");
const $$OpenGraphArticleTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$8, $$props, $$slots);
  Astro2.self = $$OpenGraphArticleTags;
  const { publishedTime, modifiedTime, expirationTime, authors, section, tags } = Astro2.props.openGraph.article;
  return renderTemplate`${publishedTime ? renderTemplate`<meta property="article:published_time"${addAttribute(publishedTime, "content")}>` : null}${modifiedTime ? renderTemplate`<meta property="article:modified_time"${addAttribute(modifiedTime, "content")}>` : null}${expirationTime ? renderTemplate`<meta property="article:expiration_time"${addAttribute(expirationTime, "content")}>` : null}${authors ? authors.map((author) => renderTemplate`<meta property="article:author"${addAttribute(author, "content")}>`) : null}${section ? renderTemplate`<meta property="article:section"${addAttribute(section, "content")}>` : null}${tags ? tags.map((tag) => renderTemplate`<meta property="article:tag"${addAttribute(tag, "content")}>`) : null}`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphArticleTags.astro", void 0);

const $$Astro$7 = createAstro("https://infpik.store");
const $$OpenGraphBasicTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$7, $$props, $$slots);
  Astro2.self = $$OpenGraphBasicTags;
  const { openGraph } = Astro2.props;
  return renderTemplate`<meta property="og:title"${addAttribute(openGraph.basic.title, "content")}><meta property="og:type"${addAttribute(openGraph.basic.type, "content")}><meta property="og:image"${addAttribute(openGraph.basic.image, "content")}><meta property="og:url"${addAttribute(openGraph.basic.url || Astro2.url.href, "content")}>`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphBasicTags.astro", void 0);

const $$Astro$6 = createAstro("https://infpik.store");
const $$OpenGraphImageTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$6, $$props, $$slots);
  Astro2.self = $$OpenGraphImageTags;
  const { image } = Astro2.props.openGraph.basic;
  const { secureUrl, type, width, height, alt } = Astro2.props.openGraph.image;
  return renderTemplate`<meta property="og:image:url"${addAttribute(image, "content")}>${secureUrl ? renderTemplate`<meta property="og:image:secure_url"${addAttribute(secureUrl, "content")}>` : null}${type ? renderTemplate`<meta property="og:image:type"${addAttribute(type, "content")}>` : null}${width ? renderTemplate`<meta property="og:image:width"${addAttribute(width, "content")}>` : null}${height ? renderTemplate`<meta property="og:image:height"${addAttribute(height, "content")}>` : null}${alt ? renderTemplate`<meta property="og:image:alt"${addAttribute(alt, "content")}>` : null}`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphImageTags.astro", void 0);

const $$Astro$5 = createAstro("https://infpik.store");
const $$OpenGraphOptionalTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$5, $$props, $$slots);
  Astro2.self = $$OpenGraphOptionalTags;
  const { optional } = Astro2.props.openGraph;
  return renderTemplate`${optional.audio ? renderTemplate`<meta property="og:audio"${addAttribute(optional.audio, "content")}>` : null}${optional.description ? renderTemplate`<meta property="og:description"${addAttribute(optional.description, "content")}>` : null}${optional.determiner ? renderTemplate`<meta property="og:determiner"${addAttribute(optional.determiner, "content")}>` : null}${optional.locale ? renderTemplate`<meta property="og:locale"${addAttribute(optional.locale, "content")}>` : null}${optional.localeAlternate?.map((locale) => renderTemplate`<meta property="og:locale:alternate"${addAttribute(locale, "content")}>`)}${optional.siteName ? renderTemplate`<meta property="og:site_name"${addAttribute(optional.siteName, "content")}>` : null}${optional.video ? renderTemplate`<meta property="og:video"${addAttribute(optional.video, "content")}>` : null}`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphOptionalTags.astro", void 0);

const $$Astro$4 = createAstro("https://infpik.store");
const $$ExtendedTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$4, $$props, $$slots);
  Astro2.self = $$ExtendedTags;
  const { props } = Astro2;
  return renderTemplate`${props.extend.link?.map((attributes) => renderTemplate`<link${spreadAttributes(attributes)}>`)}${props.extend.meta?.map(({ content, httpEquiv, media, name, property }) => renderTemplate`<meta${addAttribute(name, "name")}${addAttribute(property, "property")}${addAttribute(content, "content")}${addAttribute(httpEquiv, "http-equiv")}${addAttribute(media, "media")}>`)}`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/components/ExtendedTags.astro", void 0);

const $$Astro$3 = createAstro("https://infpik.store");
const $$TwitterTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$3, $$props, $$slots);
  Astro2.self = $$TwitterTags;
  const { card, site, title, creator, description, image, imageAlt } = Astro2.props.twitter;
  return renderTemplate`${card ? renderTemplate`<meta name="twitter:card"${addAttribute(card, "content")}>` : null}${site ? renderTemplate`<meta name="twitter:site"${addAttribute(site, "content")}>` : null}${title ? renderTemplate`<meta name="twitter:title"${addAttribute(title, "content")}>` : null}${image ? renderTemplate`<meta name="twitter:image"${addAttribute(image, "content")}>` : null}${imageAlt ? renderTemplate`<meta name="twitter:image:alt"${addAttribute(imageAlt, "content")}>` : null}${description ? renderTemplate`<meta name="twitter:description"${addAttribute(description, "content")}>` : null}${creator ? renderTemplate`<meta name="twitter:creator"${addAttribute(creator, "content")}>` : null}`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/components/TwitterTags.astro", void 0);

const $$Astro$2 = createAstro("https://infpik.store");
const $$LanguageAlternatesTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$LanguageAlternatesTags;
  const { languageAlternates } = Astro2.props;
  return renderTemplate`${languageAlternates.map((alternate) => renderTemplate`<link rel="alternate"${addAttribute(alternate.hrefLang, "hreflang")}${addAttribute(alternate.href, "href")}>`)}`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/components/LanguageAlternatesTags.astro", void 0);

const $$Astro$1 = createAstro("https://infpik.store");
const $$SEO = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$SEO;
  Astro2.props.surpressWarnings = true;
  function validateProps(props) {
    if (props.openGraph) {
      if (!props.openGraph.basic || (props.openGraph.basic.title ?? void 0) == void 0 || (props.openGraph.basic.type ?? void 0) == void 0 || (props.openGraph.basic.image ?? void 0) == void 0) {
        throw new Error(
          "If you pass the openGraph prop, you have to at least define the title, type, and image basic properties!"
        );
      }
    }
    if (props.title && props.openGraph?.basic.title) {
      if (props.title == props.openGraph.basic.title && !props.surpressWarnings) {
        console.warn(
          "WARNING(astro-seo): You passed the same value to `title` and `openGraph.optional.title`. This is most likely not what you want. See docs for more."
        );
      }
    }
    if (props.openGraph?.basic?.image && !props.openGraph?.image?.alt && !props.surpressWarnings) {
      console.warn(
        "WARNING(astro-seo): You defined `openGraph.basic.image`, but didn't define `openGraph.image.alt`. This is strongly discouraged.'"
      );
    }
  }
  validateProps(Astro2.props);
  let updatedTitle = "";
  if (Astro2.props.title) {
    updatedTitle = Astro2.props.title;
    if (Astro2.props.titleTemplate) {
      updatedTitle = Astro2.props.titleTemplate.replace(/%s/g, updatedTitle);
    }
  } else if (Astro2.props.titleDefault) {
    updatedTitle = Astro2.props.titleDefault;
  }
  const baseUrl = Astro2.site ?? Astro2.url;
  const defaultCanonicalUrl = new URL(Astro2.url.pathname + Astro2.url.search, baseUrl);
  return renderTemplate`${updatedTitle ? renderTemplate`<title>${unescapeHTML(updatedTitle)}</title>` : null}${Astro2.props.charset ? renderTemplate`<meta${addAttribute(Astro2.props.charset, "charset")}>` : null}<link rel="canonical"${addAttribute(Astro2.props.canonical || defaultCanonicalUrl.href, "href")}>${Astro2.props.description ? renderTemplate`<meta name="description"${addAttribute(Astro2.props.description, "content")}>` : null}<meta name="robots"${addAttribute(`${Astro2.props.noindex ? "noindex" : "index"}, ${Astro2.props.nofollow ? "nofollow" : "follow"}`, "content")}>${Astro2.props.openGraph && renderTemplate`${renderComponent($$result, "OpenGraphBasicTags", $$OpenGraphBasicTags, { ...Astro2.props })}`}${Astro2.props.openGraph?.optional && renderTemplate`${renderComponent($$result, "OpenGraphOptionalTags", $$OpenGraphOptionalTags, { ...Astro2.props })}`}${Astro2.props.openGraph?.image && renderTemplate`${renderComponent($$result, "OpenGraphImageTags", $$OpenGraphImageTags, { ...Astro2.props })}`}${Astro2.props.openGraph?.article && renderTemplate`${renderComponent($$result, "OpenGraphArticleTags", $$OpenGraphArticleTags, { ...Astro2.props })}`}${Astro2.props.twitter && renderTemplate`${renderComponent($$result, "TwitterTags", $$TwitterTags, { ...Astro2.props })}`}${Astro2.props.extend && renderTemplate`${renderComponent($$result, "ExtendedTags", $$ExtendedTags, { ...Astro2.props })}`}${Astro2.props.languageAlternates && renderTemplate`${renderComponent($$result, "LanguageAlternatesTags", $$LanguageAlternatesTags, { ...Astro2.props })}`}`;
}, "D:/code/image/polar-image-store/node_modules/astro-seo/src/SEO.astro", void 0);

const $$SearchModal = createComponent(async ($$result, $$props, $$slots) => {
  return renderTemplate`<!-- Search Modal - Hidden by default -->${maybeRenderHead()}<div id="searchModal" class="fixed inset-0 z-[9999] hidden" role="dialog" aria-modal="true" aria-labelledby="searchModalTitle" data-astro-cid-qk3db3zz> <!-- Backdrop --> <div id="searchModalBackdrop" class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 opacity-0" aria-hidden="true" data-astro-cid-qk3db3zz></div> <!-- Modal Content --> <div id="searchModalContent" class="fixed inset-0 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-out flex flex-col" data-astro-cid-qk3db3zz> <!-- Header with back button and search input --> <div class="sticky top-0 z-50 bg-white border-b border-gray-200" data-astro-cid-qk3db3zz> <div class="flex items-center px-4 py-3" data-astro-cid-qk3db3zz> <button id="searchModalBackButton" class="mr-3 p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors" aria-label="Close search" data-astro-cid-qk3db3zz> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" data-astro-cid-qk3db3zz></path> </svg> </button> <!-- Search Input --> <div class="flex-1 relative" data-astro-cid-qk3db3zz> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" data-astro-cid-qk3db3zz> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-astro-cid-qk3db3zz></path> </svg> </div> <input type="text" id="searchModalInput" placeholder="Search for images..." class="block w-full pl-10 pr-4 py-3 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-lg" autocomplete="off" data-astro-cid-qk3db3zz> <!-- Clear button --> <button id="searchModalClearButton" class="absolute inset-y-0 right-0 pr-3 flex items-center text-primary-400 hover:text-primary-600 transition-colors duration-200" style="display: none;" aria-label="Clear search" data-astro-cid-qk3db3zz> <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" data-astro-cid-qk3db3zz></path> </svg> </button> </div> </div> </div> <!-- Scrollable Content --> <div class="flex-1 overflow-y-auto px-4 py-6" data-astro-cid-qk3db3zz> <!-- Search Results Container --> <div id="searchModalResults" class="hidden" data-astro-cid-qk3db3zz> <div id="searchModalResultsHeader" class="mb-6" data-astro-cid-qk3db3zz> <p id="searchModalResultsText" class="text-gray-600" data-astro-cid-qk3db3zz></p> </div> <!-- Error State --> <div id="searchModalError" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <div class="p-4 text-center text-red-600" data-astro-cid-qk3db3zz> <div id="searchModalErrorText" class="text-sm" data-astro-cid-qk3db3zz></div> </div> </div> <!-- Results List --> <div id="searchModalResultsList" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <!-- Header Section --> <div id="searchModalResultsListHeader" class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100 bg-gray-50" data-astro-cid-qk3db3zz></div> <!-- Results Items --> <div id="searchModalResultsItems" class="p-2" data-astro-cid-qk3db3zz> <!-- Results will be populated here --> </div> <!-- Footer Section --> <div id="searchModalResultsFooter" class="hidden p-3 border-t border-primary-100 bg-gray-50" data-astro-cid-qk3db3zz> <div class="text-center text-primary-600 text-sm" data-astro-cid-qk3db3zz> <span id="searchModalResultsCount" data-astro-cid-qk3db3zz></span> </div> </div> </div> <!-- No Results --> <div id="searchModalNoResults" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <div class="p-4 text-center" data-astro-cid-qk3db3zz> <div id="searchModalNoResultsText" class="text-primary-600 mb-2" data-astro-cid-qk3db3zz></div> <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors" data-astro-cid-qk3db3zz>
Browse all products →
</a> </div> <!-- Popular Search Suggestions --> <div id="searchModalSuggestions" class="hidden border-t border-primary-100 p-4" data-astro-cid-qk3db3zz> <p class="text-sm font-medium text-primary-900 mb-3 text-center" data-astro-cid-qk3db3zz>Try these searches:</p> <div id="searchModalSuggestionsContainer" class="flex flex-wrap gap-2 justify-center" data-astro-cid-qk3db3zz> <!-- Suggestions will be populated here --> </div> </div> </div> </div> <!-- Initial Search State --> <div id="searchModalInitialState" data-astro-cid-qk3db3zz> <!-- Recent Searches --> <div id="searchModalRecentSearches" class="mb-8" style="display: none;" data-astro-cid-qk3db3zz> <div class="flex items-center justify-between mb-4" data-astro-cid-qk3db3zz> <h2 class="text-lg font-semibold text-primary-900" data-astro-cid-qk3db3zz>Recent Searches</h2> <button id="searchModalClearRecentSearches" class="p-2 text-primary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-all duration-200" title="Clear recent searches" data-astro-cid-qk3db3zz> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" data-astro-cid-qk3db3zz></path> </svg> </button> </div> <div id="searchModalRecentSearchesList" class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <!-- Recent searches will be populated here --> </div> </div> <!-- Popular Tags --> <div id="searchModalPopularTags" class="mb-8" data-astro-cid-qk3db3zz> <h2 class="text-lg font-semibold text-primary-900 mb-4" data-astro-cid-qk3db3zz>Popular Searches</h2> <div id="searchModalPopularTagsList" class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <div class="p-2" id="searchModalPopularTagsContainer" data-astro-cid-qk3db3zz> <!-- Popular tags will be populated here --> </div> </div> </div> <!-- Quick Actions --> <div data-astro-cid-qk3db3zz> <h2 class="text-lg font-semibold text-gray-900 mb-4" data-astro-cid-qk3db3zz>Browse</h2> <div class="space-y-3" data-astro-cid-qk3db3zz> <a href="/products" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors" data-astro-cid-qk3db3zz> <span class="font-medium text-gray-900" data-astro-cid-qk3db3zz>All Products</span> <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-astro-cid-qk3db3zz></path> </svg> </a> </div> </div> </div> </div> </div> </div>  ${renderScript($$result, "D:/code/image/polar-image-store/src/components/SearchModal.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/code/image/polar-image-store/src/components/SearchModal.astro", void 0);

const $$Astro = createAstro("https://infpik.store");
const $$Layout = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Layout;
  const {
    title,
    description = "Beautiful digital images from our collection",
    image = "/og-image.jpg",
    canonical,
    noindex = false,
    type = "website"
  } = Astro2.props;
  const siteUrl = "http://infpik.store";
  const fullImageUrl = image.startsWith("http") ? image : `${siteUrl}${image}`;
  const canonicalUrl = canonical || Astro2.url.href;
  return renderTemplate`<html lang="en"> <head>${renderComponent($$result, "SEO", $$SEO, { "title": title, "description": description, "canonical": canonicalUrl, "noindex": noindex, "charset": "UTF-8", "openGraph": {
    basic: {
      title,
      type,
      image: fullImageUrl,
      url: canonicalUrl
    },
    optional: {
      description,
      siteName: "Polar Image Store",
      locale: "en_US"
    }
  }, "twitter": {
    card: "summary_large_image",
    site: "@polarimagestore",
    creator: "@polarimagestore",
    title,
    description,
    image: fullImageUrl,
    imageAlt: `${title} - Polar Image Store`
  }, "extend": {
    link: [
      { rel: "icon", type: "image/svg+xml", href: "/favicon.svg" },
      { rel: "sitemap", href: "/sitemap-index.xml" },
      { rel: "canonical", href: canonicalUrl }
    ],
    meta: [
      { name: "viewport", content: "width=device-width, initial-scale=1.0" },
      { name: "generator", content: Astro2.generator },
      { name: "robots", content: noindex ? "noindex, nofollow" : "index, follow" },
      { name: "googlebot", content: noindex ? "noindex, nofollow" : "index, follow" },
      { name: "theme-color", content: "#6366f1" },
      { name: "msapplication-TileColor", content: "#6366f1" }
    ]
  } })}${renderHead()}</head> <body class="min-h-screen flex flex-col"> <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4"> <div class="container"> <nav class="flex items-center justify-between"> <!-- Logo --> <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors"> ${renderComponent($$result, "Image", $$Image, { "src": "/logo.svg", "alt": "Logo", "width": 32, "height": 32, "class": "w-8 h-8 text-accent-600" })}
InfPik
</a> <!-- Mobile Search Bar --> <div id="mobileHeaderSearchContainer" class="md:hidden relative flex-1 mx-2 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="mobileProductSearch" class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm" placeholder="Search for images..." autocomplete="off"> </div> <!-- Search Bar (Desktop) --> <div id="headerSearchContainer" class="hidden md:flex flex-1 max-w-md mx-8 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out"> <div class="relative w-full"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="productSearch" class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search for images..." autocomplete="off"> <!-- Search results dropdown (hidden by default) --> <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto"> <!-- Search results will be populated here --> </div> </div> </div> <!-- CTA Button & Mobile Menu --> <div class="flex items-center gap-4"> <a href="/products" class="btn-primary hidden md:inline-flex">
Browse Collection
</a> <!-- Mobile menu button --> <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </nav> <!-- Mobile menu --> <div class="md:hidden hidden" id="mobile-menu"> <div class="pt-4 pb-2 border-t border-primary-100 mt-4"> <!-- Mobile Navigation --> <ul class="space-y-2"> <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li> <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li> <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li> </ul> <!-- Legal Links --> <div class="mt-4 pt-4 border-t border-primary-100"> <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p> <ul class="space-y-2"> <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li> <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li> </ul> </div> <div class="mt-4 pt-4 border-t border-primary-100"> <a href="/products" class="btn-primary w-full justify-center">
Browse Collection
</a> </div> </div> </div> </div> </header> <main class="flex-1 pb-12"> ${renderSlot($$result, $$slots["default"])} </main> <footer class="bg-white border-t border-primary-100 py-12 text-primary-600"> <div class="container"> <div class="text-center"> <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4"> ${renderComponent($$result, "Image", $$Image, { "src": "/logo.svg", "alt": "Logo", "width": 24, "height": 24, "class": "w-6 h-6 text-accent-600" })}
InfPik
</div> <div class="flex justify-center gap-4 mb-4"> <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a> <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a> <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a> </div> <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p> </div> </div> </footer> <!-- Search Modal for Mobile --> ${renderComponent($$result, "SearchModal", $$SearchModal, {})} ${renderScript($$result, "D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts")}</body></html>`;
}, "D:/code/image/polar-image-store/src/layouts/Layout.astro", void 0);

export { $$Layout as $ };
