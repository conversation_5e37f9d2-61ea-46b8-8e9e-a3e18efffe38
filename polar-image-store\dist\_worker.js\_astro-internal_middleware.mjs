globalThis.process ??= {}; globalThis.process.env ??= {};
import { d as defineMiddleware, s as sequence } from './chunks/index_C9HqFSYk.mjs';
import { getOptimalFormat } from './chunks/imageOptimization_DKmSV198.mjs';
import './chunks/astro-designed-error-pages_BIbESOSa.mjs';
import './chunks/astro/server_BdgiS2eL.mjs';

const onRequest$2 = defineMiddleware(async (context, next) => {
  const { request, url } = context;
  if (url.pathname.startsWith("/cdn-cgi/image/")) {
    const userAgent = request.headers.get("user-agent") || "";
    const acceptHeader = request.headers.get("accept") || "";
    let optimalFormat;
    if (acceptHeader.includes("image/avif")) {
      optimalFormat = "avif";
    } else if (acceptHeader.includes("image/webp")) {
      optimalFormat = "webp";
    } else {
      optimalFormat = getOptimalFormat(userAgent);
    }
    const pathParts = url.pathname.split("/");
    if (pathParts.length >= 4) {
      const transformParams = pathParts[3];
      const imageUrl = pathParts.slice(4).join("/");
      if (transformParams.includes("format=auto")) {
        const newTransformParams = transformParams.replace("format=auto", `format=${optimalFormat}`);
        const newPath = `/cdn-cgi/image/${newTransformParams}/${imageUrl}`;
        const newUrl = new URL(newPath, url.origin);
        return Response.redirect(newUrl.toString(), 302);
      }
    }
  }
  const response = await next();
  if (url.pathname.match(/\.(jpg|jpeg|png|webp|avif|gif|svg)$/i)) {
    response.headers.set("Cache-Control", "public, max-age=31536000, immutable");
    response.headers.set("Vary", "Accept");
  }
  if (url.pathname.startsWith("/cdn-cgi/image/")) {
    response.headers.set("Vary", "Accept");
    response.headers.set("Accept-CH", "Viewport-Width, Width, DPR");
  }
  return response;
});

const onRequest$1 = (context, next) => {
  if (context.isPrerendered) {
    context.locals.runtime ??= {
      env: process.env
    };
  }
  return next();
};

const onRequest = sequence(
	onRequest$1,
	onRequest$2
	
);

export { onRequest };
