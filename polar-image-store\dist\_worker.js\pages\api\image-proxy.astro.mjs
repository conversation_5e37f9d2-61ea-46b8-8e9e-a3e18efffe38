globalThis.process ??= {}; globalThis.process.env ??= {};
import { getOptimalFormat, getOptimizedImageUrl } from '../../chunks/imageOptimization_DKmSV198.mjs';
export { renderers } from '../../renderers.mjs';

const GET = async ({ request, url }) => {
  try {
    const searchParams = url.searchParams;
    const imageUrl = searchParams.get("url");
    const width = searchParams.get("width");
    const height = searchParams.get("height");
    const quality = searchParams.get("quality");
    const format = searchParams.get("format");
    const fit = searchParams.get("fit");
    if (!imageUrl) {
      return new Response("Missing image URL", { status: 400 });
    }
    const userAgent = request.headers.get("user-agent") || "";
    const acceptHeader = request.headers.get("accept") || "";
    let optimalFormat;
    if (format && format !== "auto") {
      optimalFormat = format;
    } else {
      if (acceptHeader.includes("image/avif")) {
        optimalFormat = "avif";
      } else if (acceptHeader.includes("image/webp")) {
        optimalFormat = "webp";
      } else {
        optimalFormat = getOptimalFormat(userAgent);
      }
    }
    const transformOptions = {
      format: optimalFormat,
      fit: fit || "scale-down"
    };
    if (width) transformOptions.width = parseInt(width);
    if (height) transformOptions.height = parseInt(height);
    if (quality) transformOptions.quality = parseInt(quality);
    const optimizedUrl = getOptimizedImageUrl(imageUrl, transformOptions);
    if (optimizedUrl === imageUrl || imageUrl.startsWith("/")) {
      return Response.redirect(imageUrl, 302);
    }
    const baseUrl = new URL(request.url).origin;
    const fullOptimizedUrl = `${baseUrl}${optimizedUrl}`;
    return Response.redirect(fullOptimizedUrl, 302);
  } catch (error) {
    console.error("Image proxy error:", error);
    return new Response("Internal server error", { status: 500 });
  }
};
const POST = async () => {
  return new Response("Method not allowed", { status: 405 });
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  POST
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
