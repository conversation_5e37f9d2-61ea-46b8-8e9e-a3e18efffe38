globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createAstro, a as createComponent, r as renderComponent, d as renderTemplate, m as maybeRenderHead } from '../../../chunks/astro/server_BdgiS2eL.mjs';
import { $ as $$Layout } from '../../../chunks/Layout_CUk76QKG.mjs';
import { $ as $$ProductCard } from '../../../chunks/ProductCard_CwMHrQOb.mjs';
import { a as $$StructuredData } from '../../../chunks/StructuredData_DloT4738.mjs';
import { c as createPolarClient, t as transformPolarProduct, d as getProductsByTag, g as getTagDisplayName } from '../../../chunks/polar_CPL-REiF.mjs';
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro("https://infpik.store");
const $$tag = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$tag;
  const { tag } = Astro2.params;
  if (!tag) {
    return Astro2.redirect("/products");
  }
  let allProducts = [];
  let products = [];
  let error = null;
  try {
    const env = Astro2.locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ; else {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });
      const productList = response.result?.items || [];
      allProducts = productList.map(transformPolarProduct).filter((product) => product !== null);
      products = getProductsByTag(allProducts, tag);
    }
  } catch (err) {
    console.error("Error fetching products for tag:", err);
    error = "Failed to load products";
  }
  const tagDisplayName = getTagDisplayName(tag);
  const breadcrumbData = {
    items: [
      { name: "Home", url: "http://infpik.store" },
      { name: "Products", url: `${"http://infpik.store"}/products` },
      { name: `#${tagDisplayName}`, url: `${"http://infpik.store"}/products/tag/${tag}` }
    ]
  };
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${tagDisplayName} - Polar Image Store`, "description": `Browse our collection of ${tagDisplayName.toLowerCase()} digital images and artwork. High-quality digital assets tagged with ${tagDisplayName.toLowerCase()}.`, "canonical": `${"http://infpik.store"}/products/tag/${tag}` }, { "default": async ($$result2) => renderTemplate`  ${renderComponent($$result2, "StructuredData", $$StructuredData, { "type": "BreadcrumbList", "data": breadcrumbData })} ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <section class="text-center mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4">#${tagDisplayName}</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto"> ${products.length > 0 ? `Discover ${products.length} ${products.length === 1 ? "item" : "items"} tagged with ${tagDisplayName.toLowerCase()}` : `No items found with tag ${tagDisplayName.toLowerCase()}`} </p> </section> ${products.length === 0 ? renderTemplate`<div class="text-center py-16"> <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"> <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path> </svg> </div> <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Items with #${tagDisplayName}</h3> <p class="text-gray-600 mb-8">We don't have any items tagged with ${tagDisplayName.toLowerCase()} yet.</p> <a href="/products" class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg>
Browse All Products
</a> </div>` : renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${products.map((product) => renderTemplate`${renderComponent($$result2, "ProductCard", $$ProductCard, { "product": product })}`)} </div>`} </div> ` })}`;
}, "D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro", void 0);
const $$file = "D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro";
const $$url = "/products/tag/[tag]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$tag,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
