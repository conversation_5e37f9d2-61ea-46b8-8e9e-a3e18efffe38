globalThis.process ??= {}; globalThis.process.env ??= {};
function getOptimizedImageUrl(imageUrl, options = {}) {
  if (imageUrl.startsWith("/") || imageUrl.includes("placeholder")) {
    return imageUrl;
  }
  const {
    width,
    height,
    quality = 85,
    format = "auto",
    fit = "scale-down",
    sharpen,
    blur,
    saturation,
    brightness,
    contrast,
    gamma
  } = options;
  const params = [];
  if (width) params.push(`width=${width}`);
  if (height) params.push(`height=${height}`);
  if (quality) params.push(`quality=${quality}`);
  if (format) params.push(`format=${format}`);
  if (fit) params.push(`fit=${fit}`);
  if (sharpen) params.push(`sharpen=${sharpen}`);
  if (blur) params.push(`blur=${blur}`);
  if (saturation) params.push(`saturation=${saturation}`);
  if (brightness) params.push(`brightness=${brightness}`);
  if (contrast) params.push(`contrast=${contrast}`);
  if (gamma) params.push(`gamma=${gamma}`);
  const transformParams = params.join(",");
  return `/cdn-cgi/image/${transformParams}/${imageUrl}`;
}
function getResponsiveImageUrls(imageUrl, options = {}) {
  const {
    sizes = [320, 640, 960, 1280, 1920],
    densities = [1, 2],
    ...transformOptions
  } = options;
  const mainSrc = getOptimizedImageUrl(imageUrl, {
    ...transformOptions,
    width: Math.max(...sizes)
  });
  const srcsetEntries = [];
  for (const size of sizes) {
    for (const density of densities) {
      const width = size * density;
      const url = getOptimizedImageUrl(imageUrl, {
        ...transformOptions,
        width
      });
      srcsetEntries.push(`${url} ${width}w`);
    }
  }
  return {
    src: mainSrc,
    srcset: srcsetEntries.join(", ")
  };
}
const ImagePresets = {
  // Product card images
  productCard: (imageUrl) => getOptimizedImageUrl(imageUrl, {
    width: 800,
    height: 600,
    quality: 85,
    format: "auto",
    fit: "cover"
  }),
  // Product detail main image
  productDetail: (imageUrl) => getOptimizedImageUrl(imageUrl, {
    width: 1200,
    height: 900,
    quality: 90,
    format: "auto",
    fit: "contain"
  }),
  // Thumbnail images
  thumbnail: (imageUrl) => getOptimizedImageUrl(imageUrl, {
    width: 150,
    height: 150,
    quality: 80,
    format: "auto",
    fit: "cover"
  }),
  // Hero/banner images
  hero: (imageUrl) => getOptimizedImageUrl(imageUrl, {
    width: 1920,
    height: 1080,
    quality: 90,
    format: "auto",
    fit: "cover"
  }),
  // Related images
  related: (imageUrl) => getOptimizedImageUrl(imageUrl, {
    width: 600,
    height: 450,
    quality: 85,
    format: "auto",
    fit: "cover"
  })
};
function generateSizesAttribute(breakpoints = {}) {
  const defaultBreakpoints = {
    "(max-width: 640px)": "100vw",
    "(max-width: 1024px)": "50vw",
    "(max-width: 1280px)": "33vw",
    ...breakpoints
  };
  const sizeEntries = Object.entries(defaultBreakpoints);
  const sizesArray = sizeEntries.slice(0, -1).map(([query, size]) => `${query} ${size}`);
  const defaultSize = sizeEntries[sizeEntries.length - 1][1];
  sizesArray.push(defaultSize);
  return sizesArray.join(", ");
}
function supportsAVIF(userAgent) {
  if (!userAgent) return false;
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
  const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
  const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);
  if (chromeMatch && parseInt(chromeMatch[1]) >= 85) return true;
  if (firefoxMatch && parseInt(firefoxMatch[1]) >= 93) return true;
  if (safariMatch && parseInt(safariMatch[1]) >= 16) return true;
  return false;
}
function supportsWebP(userAgent) {
  if (!userAgent) return false;
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
  const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
  const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);
  const edgeMatch = userAgent.match(/Edge\/(\d+)/);
  if (chromeMatch && parseInt(chromeMatch[1]) >= 23) return true;
  if (firefoxMatch && parseInt(firefoxMatch[1]) >= 65) return true;
  if (safariMatch && parseInt(safariMatch[1]) >= 14) return true;
  if (edgeMatch && parseInt(edgeMatch[1]) >= 18) return true;
  return false;
}
function getOptimalFormat(userAgent) {
  if (supportsAVIF(userAgent)) return "avif";
  if (supportsWebP(userAgent)) return "webp";
  return "jpeg";
}

export { ImagePresets, generateSizesAttribute, getOptimalFormat, getOptimizedImageUrl, getResponsiveImageUrls, supportsAVIF, supportsWebP };
