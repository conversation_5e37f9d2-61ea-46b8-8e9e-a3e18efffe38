globalThis.process ??= {}; globalThis.process.env ??= {};
import { g as decodeKey } from './chunks/astro/server_BdgiS2eL.mjs';
import './chunks/astro-designed-error-pages_BIbESOSa.mjs';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/noop-middleware_DqBIwGzW.mjs';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///D:/code/image/polar-image-store/","cacheDir":"file:///D:/code/image/polar-image-store/node_modules/.astro/","outDir":"file:///D:/code/image/polar-image-store/dist/","srcDir":"file:///D:/code/image/polar-image-store/src/","publicDir":"file:///D:/code/image/polar-image-store/public/","buildClientDir":"file:///D:/code/image/polar-image-store/dist/","buildServerDir":"file:///D:/code/image/polar-image-store/dist/_worker.js/","adapterName":"@astrojs/cloudflare","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/generic.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"}],"routeData":{"route":"/about","isIndex":false,"type":"page","pattern":"^\\/about\\/?$","segments":[[{"content":"about","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/about.astro","pathname":"/about","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/checkout","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/checkout\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"checkout","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/checkout.ts","pathname":"/api/checkout","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/image-proxy","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/image-proxy\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"image-proxy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/image-proxy.ts","pathname":"/api/image-proxy","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/products","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/products\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/products.ts","pathname":"/api/products","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/search","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/search\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/search.ts","pathname":"/api/search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/tags","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/tags\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"tags","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/tags.ts","pathname":"/api/tags","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/webhooks","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/webhooks\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"webhooks","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/webhooks.ts","pathname":"/api/webhooks","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"}],"routeData":{"route":"/privacy","isIndex":false,"type":"page","pattern":"^\\/privacy\\/?$","segments":[[{"content":"privacy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/privacy.astro","pathname":"/privacy","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"}],"routeData":{"route":"/products/category/[category]","isIndex":false,"type":"page","pattern":"^\\/products\\/category\\/([^/]+?)\\/?$","segments":[[{"content":"products","dynamic":false,"spread":false}],[{"content":"category","dynamic":false,"spread":false}],[{"content":"category","dynamic":true,"spread":false}]],"params":["category"],"component":"src/pages/products/category/[category].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"}],"routeData":{"route":"/products/tag/[tag]","isIndex":false,"type":"page","pattern":"^\\/products\\/tag\\/([^/]+?)\\/?$","segments":[[{"content":"products","dynamic":false,"spread":false}],[{"content":"tag","dynamic":false,"spread":false}],[{"content":"tag","dynamic":true,"spread":false}]],"params":["tag"],"component":"src/pages/products/tag/[tag].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"},{"type":"inline","content":"@keyframes fade-in{0%{opacity:0}to{opacity:1}}.animate-fade-in[data-astro-cid-gjhjmbi3]{animation:fade-in .3s ease}#lightbox[data-astro-cid-gjhjmbi3].active{display:flex!important;align-items:center;justify-content:center}@media (max-width: 768px){#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-left-20{left:10px;font-size:2rem;padding:.5rem}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-right-20{right:10px;font-size:2rem;padding:.5rem}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-top-12{top:10px;right:10px;font-size:1.5rem}#lightbox[data-astro-cid-gjhjmbi3] .absolute[data-astro-cid-gjhjmbi3].-bottom-12{bottom:10px}}.scrollbar-hide[data-astro-cid-4j3m3ndg]{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide[data-astro-cid-4j3m3ndg]::-webkit-scrollbar{display:none}.line-clamp-2[data-astro-cid-4j3m3ndg]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\n"}],"routeData":{"route":"/products/[slug]","isIndex":false,"type":"page","pattern":"^\\/products\\/([^/]+?)\\/?$","segments":[[{"content":"products","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/products/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"}],"routeData":{"route":"/products","isIndex":true,"type":"page","pattern":"^\\/products\\/?$","segments":[[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/products/index.astro","pathname":"/products","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"}],"routeData":{"route":"/success","isIndex":false,"type":"page","pattern":"^\\/success\\/?$","segments":[[{"content":"success","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/success.astro","pathname":"/success","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"}],"routeData":{"route":"/terms","isIndex":false,"type":"page","pattern":"^\\/terms\\/?$","segments":[[{"content":"terms","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/terms.astro","pathname":"/terms","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/about.DtI5uJNc.css"},{"type":"inline","content":".bg-grid-pattern[data-astro-cid-bbe6dxrz]{background-image:linear-gradient(rgba(0,0,0,.1) 1px,transparent 1px),linear-gradient(90deg,rgba(0,0,0,.1) 1px,transparent 1px);background-size:20px 20px}.scrollbar-hide[data-astro-cid-bbe6dxrz]{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide[data-astro-cid-bbe6dxrz]::-webkit-scrollbar{display:none}\n"}],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"https://infpik.store","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["D:/code/image/polar-image-store/src/pages/about.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/privacy.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/[slug].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/category/[category].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/success.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/terms.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\u0000@astro-page:src/pages/api/checkout@_@ts":"pages/api/checkout.astro.mjs","\u0000@astro-page:src/pages/api/image-proxy@_@ts":"pages/api/image-proxy.astro.mjs","\u0000@astro-page:src/pages/api/products@_@ts":"pages/api/products.astro.mjs","\u0000@astro-page:src/pages/api/search@_@ts":"pages/api/search.astro.mjs","\u0000@astro-page:src/pages/api/tags@_@ts":"pages/api/tags.astro.mjs","\u0000@astro-page:src/pages/api/webhooks@_@ts":"pages/api/webhooks.astro.mjs","\u0000@astro-page:src/pages/privacy@_@astro":"pages/privacy.astro.mjs","\u0000@astro-page:src/pages/products/category/[category]@_@astro":"pages/products/category/_category_.astro.mjs","\u0000@astro-page:src/pages/products/tag/[tag]@_@astro":"pages/products/tag/_tag_.astro.mjs","\u0000@astro-page:src/pages/products/index@_@astro":"pages/products.astro.mjs","\u0000@astro-page:src/pages/success@_@astro":"pages/success.astro.mjs","\u0000@astro-page:src/pages/terms@_@astro":"pages/terms.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"index.js","\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/generic@_@js":"pages/_image.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astro-page:src/pages/products/[slug]@_@astro":"pages/products/_slug_.astro.mjs","D:/code/image/polar-image-store/src/utils/imageOptimization.ts":"chunks/imageOptimization_DKmSV198.mjs","D:/code/image/polar-image-store/node_modules/unstorage/drivers/cloudflare-kv-binding.mjs":"chunks/cloudflare-kv-binding_DMly_2Gl.mjs","\u0000@astrojs-manifest":"manifest_DHd52p_Z.mjs","D:/code/image/polar-image-store/node_modules/@astrojs/cloudflare/dist/entrypoints/image-service.js":"chunks/image-service_CI1DyvsZ.mjs","D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts":"_astro/index.astro_astro_type_script_index_0_lang.FvzX8YTC.js","D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts":"_astro/Layout.astro_astro_type_script_index_0_lang.LyuaaIwD.js","D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts":"_astro/Hero.astro_astro_type_script_index_0_lang.Du7FjX0n.js","D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts":"_astro/ImageGallery.astro_astro_type_script_index_0_lang.dQDmp6mo.js","D:/code/image/polar-image-store/src/components/SearchModal.astro?astro&type=script&index=0&lang.ts":"_astro/SearchModal.astro_astro_type_script_index_0_lang.Co9dbPbB.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{window.addEventListener(\"categoryChange\",e=>{const l=e.detail.categoryId;r(l)});function r(e){document.querySelectorAll(\".product-item\").forEach(t=>{const s=t.dataset.category,a=t.dataset.featured===\"true\";e===\"all\"||s===e?e===\"all\"&&!a?t.style.display=\"none\":t.style.display=\"block\":t.style.display=\"none\"});const o=document.querySelector(\"#view-all-products-btn\");if(o&&e!==\"all\"){const t=n(e);o.innerHTML=`\n          View All ${t} Products\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n          </svg>\n        `,o.href=`/products/category/${e}`}else o&&e===\"all\"&&(o.innerHTML=`\n          View All Products\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n          </svg>\n        `,o.href=\"/products\")}function n(e){return e.split(\"-\").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(\" \")}});"],["D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const e=document.getElementById(\"categoryScroll\");if(e){let t=!1,o,i;e.addEventListener(\"touchstart\",r=>{t=!0,o=r.touches[0].pageX-e.offsetLeft,i=e.scrollLeft}),e.addEventListener(\"touchend\",()=>{t=!1}),e.addEventListener(\"touchmove\",r=>{if(!t)return;const a=(r.touches[0].pageX-e.offsetLeft-o)*2;e.scrollLeft=i-a}),e.addEventListener(\"mousedown\",r=>{t=!0,o=r.pageX-e.offsetLeft,i=e.scrollLeft,e.style.cursor=\"grabbing\"}),e.addEventListener(\"mouseleave\",()=>{t=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mouseup\",()=>{t=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mousemove\",r=>{if(!t)return;r.preventDefault();const a=(r.pageX-e.offsetLeft-o)*2;e.scrollLeft=i-a}),e.style.cursor=\"grab\";const f=document.querySelectorAll(\".category-tab\");f.forEach(r=>{r.addEventListener(\"click\",s=>{const a=s.currentTarget.dataset.category;f.forEach(n=>{n.classList.remove(\"bg-accent-500\",\"text-white\",\"border-accent-500\",\"shadow-md\"),n.classList.add(\"bg-white\",\"text-primary-900\",\"border-primary-200\",\"shadow-sm\",\"hover:bg-primary-50\",\"hover:border-accent-500\");const h=n.querySelector(\"span\");h&&(h.classList.remove(\"bg-white/20\",\"text-white\"),h.classList.add(\"bg-primary-200\",\"text-primary-600\"))}),r.classList.remove(\"bg-white\",\"text-primary-900\",\"border-primary-200\",\"shadow-sm\",\"hover:bg-primary-50\",\"hover:border-accent-500\"),r.classList.add(\"bg-accent-500\",\"text-white\",\"border-accent-500\",\"shadow-md\");const c=r.querySelector(\"span\");c&&(c.classList.remove(\"bg-primary-200\",\"text-primary-600\"),c.classList.add(\"bg-white/20\",\"text-white\")),window.location.pathname===\"/\"?(console.log(\"📡 Dispatching categoryChange event for homepage:\",a),window.dispatchEvent(new CustomEvent(\"categoryChange\",{detail:{categoryId:a}}))):a===\"all\"?window.location.href=\"/products\":window.location.href=`/products/category/${a}`})});const w=document.querySelectorAll(\".tag-tab\");w.forEach(r=>{r.addEventListener(\"click\",s=>{const a=s.currentTarget.dataset.tag;w.forEach(l=>{l.classList.remove(\"bg-accent-500\",\"text-white\",\"border-accent-500\",\"shadow-md\"),l.classList.add(\"bg-white\",\"text-primary-900\",\"border-primary-200\",\"shadow-sm\",\"hover:bg-primary-50\",\"hover:border-accent-500\");const n=l.querySelector(\"span\");n&&(n.classList.remove(\"bg-white/20\",\"text-white\"),n.classList.add(\"bg-primary-200\",\"text-primary-600\"))}),s.currentTarget.classList.remove(\"bg-white\",\"text-primary-900\",\"border-primary-200\",\"shadow-sm\",\"hover:bg-primary-50\",\"hover:border-accent-500\"),s.currentTarget.classList.add(\"bg-accent-500\",\"text-white\",\"border-accent-500\",\"shadow-md\");const c=s.currentTarget.querySelector(\"span\");c&&(c.classList.remove(\"bg-primary-200\",\"text-primary-600\"),c.classList.add(\"bg-white/20\",\"text-white\")),setTimeout(()=>{a===\"all\"?window.location.href=\"/products\":window.location.href=`/products/tag/${a}`},150)})})}const g=document.getElementById(\"heroSearchForm\"),d=document.getElementById(\"heroSearchInput\");function m(){return window.innerWidth<768}function u(t,o){if(m()){t.blur();const i=t.value.trim();window.openSearchModal?.(i)}}d&&g&&(d.addEventListener(\"focus\",t=>{u(t.target)}),d.addEventListener(\"click\",t=>{u(t.target)}),g.addEventListener(\"submit\",t=>{const o=d.value.trim();o&&m()&&(t.preventDefault(),window.openSearchModal?.(o))}))});"],["D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts","let o=0,n=[];document.addEventListener(\"DOMContentLoaded\",function(){const e=document.querySelectorAll(\".thumbnail img\");if(n=Array.from(e).map(t=>t.src),n.length===0){const t=document.getElementById(\"mainImage\");t&&(n=[t.src])}});function c(){const e=document.getElementById(\"lightbox\");e&&(e.classList.remove(\"active\"),document.body.style.overflow=\"auto\")}function g(){n.length>1&&(o=(o-1+n.length)%n.length,i())}function a(){n.length>1&&(o=(o+1)%n.length,i())}function i(){const e=document.getElementById(\"lightboxImage\"),t=document.getElementById(\"imageCounter\");e&&t&&(e.src=n[o],e.alt=`Image ${o+1}`,t.textContent=`${o+1} / ${n.length}`)}document.addEventListener(\"keydown\",function(e){const t=document.getElementById(\"lightbox\");if(t&&t.classList.contains(\"active\"))switch(e.key){case\"Escape\":c();break;case\"ArrowLeft\":g();break;case\"ArrowRight\":a();break}});document.addEventListener(\"click\",function(e){const t=document.getElementById(\"lightbox\");e.target===t&&c()});"]],"assets":["/_astro/about.DtI5uJNc.css","/favicon.svg","/logo.svg","/og-image.jpg","/placeholder-image.svg","/robots.txt","/_astro/Layout.astro_astro_type_script_index_0_lang.LyuaaIwD.js","/_astro/SearchModal.astro_astro_type_script_index_0_lang.Co9dbPbB.js","/_worker.js/index.js","/_worker.js/renderers.mjs","/_worker.js/<EMAIL>","/_worker.js/_astro-internal_middleware.mjs","/_worker.js/_noop-actions.mjs","/_worker.js/_astro/about.DtI5uJNc.css","/_worker.js/chunks/astro-designed-error-pages_BIbESOSa.mjs","/_worker.js/chunks/astro_BXq6urGO.mjs","/_worker.js/chunks/cloudflare-kv-binding_DMly_2Gl.mjs","/_worker.js/chunks/image-service_CI1DyvsZ.mjs","/_worker.js/chunks/imageOptimization_DKmSV198.mjs","/_worker.js/chunks/index_C4LTQoxk.mjs","/_worker.js/chunks/index_C9HqFSYk.mjs","/_worker.js/chunks/Layout_CUk76QKG.mjs","/_worker.js/chunks/noop-middleware_DqBIwGzW.mjs","/_worker.js/chunks/path_h5kZAkfu.mjs","/_worker.js/chunks/polar_CPL-REiF.mjs","/_worker.js/chunks/ProductCard_CwMHrQOb.mjs","/_worker.js/chunks/sdk_DEQ9AU5A.mjs","/_worker.js/chunks/server_DFjLKER-.mjs","/_worker.js/chunks/StructuredData_DloT4738.mjs","/_worker.js/chunks/_astro_assets_BhKiY6XE.mjs","/_worker.js/pages/about.astro.mjs","/_worker.js/pages/index.astro.mjs","/_worker.js/pages/privacy.astro.mjs","/_worker.js/pages/products.astro.mjs","/_worker.js/pages/success.astro.mjs","/_worker.js/pages/terms.astro.mjs","/_worker.js/pages/_image.astro.mjs","/_worker.js/chunks/astro/server_BdgiS2eL.mjs","/_worker.js/pages/api/checkout.astro.mjs","/_worker.js/pages/api/image-proxy.astro.mjs","/_worker.js/pages/api/products.astro.mjs","/_worker.js/pages/api/search.astro.mjs","/_worker.js/pages/api/tags.astro.mjs","/_worker.js/pages/api/webhooks.astro.mjs","/_worker.js/pages/products/_slug_.astro.mjs","/_worker.js/pages/products/category/_category_.astro.mjs","/_worker.js/pages/products/tag/_tag_.astro.mjs"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"6jyoEh8iNHMiv2CwSZ7vL07HiZbZOIYaKqosNywZaCM=","sessionConfig":{"driver":"cloudflare-kv-binding","options":{"binding":"SESSION"}}});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = () => import('./chunks/cloudflare-kv-binding_DMly_2Gl.mjs');

export { manifest };
